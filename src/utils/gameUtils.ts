export const GAME_MESSAGES = {
  GENERATE_CHARACTER: "Dime un personaje",
  INITIAL_GAME_QUERY: "¡Hola! Estoy listo para adivinar lo que sea. ¿Ya tienes un personaje en mente?",
};

export const GAME_STEPS = {
  GENERATE: 'generate',
  START: 'start',
  PLAYING: 'playing'
} as const;

export type GameStep = typeof GAME_STEPS[keyof typeof GAME_STEPS];

export const getGameStepTitle = (step: GameStep): string => {
  switch (step) {
    case GAME_STEPS.GENERATE:
      return "Paso 1: Generar Personaje";
    case GAME_STEPS.START:
      return "Paso 2: Iniciar el Juego";
    case GAME_STEPS.PLAYING:
      return "Paso 3: Responder a la IA";
    default:
      return "";
  }
};

export const getGameStepDescription = (step: GameStep): string => {
  switch (step) {
    case GAME_STEPS.GENERATE:
      return "Primero, genera un personaje que la IA tendrá que adivinar:";
    case GAME_STEPS.START:
      return "Ahora inicia el juego donde la IA intentará adivinar tu personaje:";
    case GAME_STEPS.PLAYING:
      return 'Responde a las preguntas de la IA (responde con "sí", "no", "a veces", etc.):';
    default:
      return "";
  }
};
